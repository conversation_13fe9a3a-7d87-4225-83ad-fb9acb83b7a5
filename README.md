# HSM WebSocket Server

A TypeScript-based WebSocket server using Socket.IO for real-time communication.

## 🚀 Features

- **TypeScript**: Full TypeScript support with type safety
- **Socket.IO**: Real-time bidirectional event-based communication
- **Express**: Static file serving for client testing
- **Development Tools**: Hot reload with nodemon and ts-node

## 📦 Installation

```bash
npm install
```

## 🛠️ Development

### Start the development server with hot reload:
```bash
npm run dev
```

### Watch mode (compile TypeScript in watch mode):
```bash
npm run watch
```

### Development with nodemon (auto-restart on file changes):
```bash
npm run dev:watch
```

## 🏗️ Production

### Build the project:
```bash
npm run build
```

### Start the production server:
```bash
npm start
```

## 🐳 Docker Deployment

### Quick Start with Docker Compose

1. **Production deployment:**
   ```bash
   # Copy environment template
   cp .env.docker .env

   # Edit .env with your configuration
   nano .env

   # Start all services
   docker-compose up -d
   ```

2. **Development with Docker:**
   ```bash
   # Start development environment
   docker-compose -f docker-compose.dev.yml up -d
   ```

### Docker Commands

#### Production:
```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f hsm-websocket

# Stop all services
docker-compose down

# Rebuild and restart
docker-compose up -d --build
```

#### Development:
```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# View application logs
docker-compose -f docker-compose.dev.yml logs -f hsm-websocket-dev

# Stop development environment
docker-compose -f docker-compose.dev.yml down
```

### Docker Services

#### Production (`docker-compose.yml`):
- **hsm-websocket**: Main application container
- **postgres**: PostgreSQL database
- **redis**: Redis cache (optional)

#### Development (`docker-compose.dev.yml`):
- **hsm-websocket-dev**: Development container with hot reload
- **postgres-dev**: Development database
- **redis-dev**: Development Redis
- **pgadmin**: Database management UI (http://localhost:5050)

### Environment Configuration

Create `.env` file for Docker Compose:
```env
# Database
DB_NAME=hsm_websocket
DB_USER=postgres
DB_PASSWORD=your_secure_password
DB_PORT=5432

# Application
NODE_ENV=production
PORT=8081
```

### Health Checks

All services include health checks:
- **Application**: `http://localhost:8081/health`
- **Database**: PostgreSQL connection test
- **Redis**: Redis ping test

### Volumes and Persistence

- **postgres_data**: Database data persistence
- **redis_data**: Redis data persistence
- **logs**: Application logs (mounted to `./logs`)

### Manual Docker Build

```bash
# Build image
docker build -t hsm-websocket:latest .

# Run container
docker run -d \
  --name hsm-websocket \
  -p 8081:8081 \
  --env-file .env \
  hsm-websocket:latest
```

## 🧪 Testing

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Open your browser and navigate to:
   ```
   http://localhost:3000
   ```

3. You should see a Socket.IO test client where you can:
   - See connection status
   - Send messages to the server
   - Receive responses from the server

## 📁 Project Structure

```
hsm-websocket/
├── src/
│   └── index.ts          # Main server file
├── public/
│   └── index.html        # Test client
├── dist/                 # Compiled JavaScript (after build)
├── package.json          # Dependencies and scripts
├── tsconfig.json         # TypeScript configuration
├── nodemon.json          # Nodemon configuration
└── README.md             # This file
```

## 📡 Socket.IO Events

### Client to Server:
- `message`: Send a message to the server

### Server to Client:
- `response`: Receive a response from the server

## 🎯 Usage Example

```typescript
// Client-side
socket.emit('message', 'Hello Server!');

socket.on('response', (data) => {
    console.log('Server responded:', data);
});
```

## � Configuration

### Environment Variables

- `PORT`: Server port (default: 3000)

### TypeScript Configuration

The project uses strict TypeScript settings with:
- ES2022 target
- Source maps enabled
- Declaration files generated
- Strict type checking

## �🚀 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run dev` | Start development server with ts-node |
| `npm run dev:watch` | Start development server with auto-restart |
| `npm run build` | Compile TypeScript to JavaScript |
| `npm run start` | Start production server |
| `npm run watch` | Compile TypeScript in watch mode |

## 🔗 Dependencies

### Production:
- `socket.io`: WebSocket library
- `express`: Web framework for static files

### Development:
- `typescript`: TypeScript compiler
- `ts-node`: TypeScript execution engine
- `nodemon`: File watcher for auto-restart
- `@types/node`: Node.js type definitions
- `@types/express`: Express type definitions

## 📝 License

ISC
# hia-sang-ma-socket
