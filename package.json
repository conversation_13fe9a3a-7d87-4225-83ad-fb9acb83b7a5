{"name": "hsm-websocket", "version": "1.0.0", "description": "WebSocket server using Socket.IO with TypeScript", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "env-cmd -f .env.dev ts-node src/index.ts", "watch": "tsc -w", "dev:watch": "env-cmd -f .env.dev nodemon --exec ts-node src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@types/pg": "^8.15.4", "dotenv": "^17.0.0", "express": "^4.18.2", "pg": "^8.16.3", "socket.io": "^4.7.5"}, "devDependencies": {"@types/dotenv": "^6.1.1", "@types/express": "^4.17.21", "@types/node": "^20.10.0", "env-cmd": "^10.1.0", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^5.3.2"}, "keywords": ["websocket", "socket.io", "typescript", "nodejs"], "author": "", "license": "ISC"}