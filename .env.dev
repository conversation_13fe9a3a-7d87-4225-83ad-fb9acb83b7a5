# Environment variables for development
PORT=8081
NODE_ENV=development

# Socket.IO Configuration
SOCKET_CORS_ORIGIN=*
SOCKET_CORS_METHODS=GET,POST

# Application Settings
APP_NAME=HSM WebSocket Server
LOG_LEVEL=info

# Database Configuration (PostgreSQL)
# DB_HOST=tramway.proxy.rlwy.net
# DB_PORT=56275
# DB_NAME=hiasangma
# DB_USER=postgres
# DB_PASSWORD=eAcxteMDydBcFUUsmnJcDFTkJyAfIBXq
# DB_SSL=false
DB_HOST=centerbeam.proxy.rlwy.net
DB_PORT=49050
DB_NAME=railway
DB_USER=postgres
DB_PASSWORD=DgionQQsYzzprfMSeTmzqsUbVBMPdsky
DB_SSL=false