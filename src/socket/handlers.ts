import { Socket } from "socket.io";
import {
  ClientToServerEvents,
  ServerToClientEvents,
  SocketData,
} from "./types";
import {
  handleConnection,
  handleDisconnection,
  handleSendMessage,
  handleChatRoom,
  handlePushNotification,
} from "./events";

export const setupSocketHandlers = (
  socket: Socket<ClientToServerEvents, ServerToClientEvents, any, SocketData>
) => {
  // Handle initial connection
  handleConnection(socket);

  // Handle message events
  handleSendMessage(socket);

  // Handle room events
  handleChatRoom(socket);

  // Handle disconnection
  handleDisconnection(socket);

  handlePushNotification(socket);
};
