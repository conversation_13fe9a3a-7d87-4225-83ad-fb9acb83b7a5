import { MessageType, MessageStatus, CreateChatMessageData } from "../types";

export interface ServerToClientEvents {
  connected: (message: string) => void;
  receive_join: (data: { message: string; user: User }) => void;
  receive_leave: (data: { message: string; user: User }) => void;

  chat_notification: (data: {
    message: string;
    data: {
      user: User;
      message: CreateChatMessageData;
    };
  }) => void;
  recieve_notification: (data: {
    services : NotificationServices
  }) => void;
  new_message: (message: Message) => void;
  // message_sent: (message: Message) => void;
  // message_status: (message: Message) => void;
}

export interface ClientToServerEvents {
  join_chat: (message: { chat_id: number }) => void;
  leave_chat: (message: { chat_id: number }) => void;

  send_message: (send: SendMessage) => void;

  send_notification: (data: SendNofication) => void;
}

export interface InterServerEvents {
  ping: () => void;
}

export interface SocketData {
  user: User;
  isOnline?: boolean;
}

// Define interfaces for Socket.IO type safety
export interface User {
  id: number;
  email: string;
  phone?: string;
  firstName: string;
  lastName: string;
  imageUrl?: string | null;
  departmentIds?: number[];
  organizationIds?: number[];
}
export type ChatType = "private" | "task" | "department" | "organization";

export interface Chat {
  chatId: number;
  lastMessage?: Message | null;
  timestamp: Date;
  type: ChatType;
  participants: User[];
}

export interface Message {
  messageId: number;
  content: string;
  sender: User;
  createdAt: any;
  messageType?: MessageType;
  status: MessageStatus;
}

export interface SendMessage {
  chatId: number;
  chatType: ChatType;
  content: string;
  sender: User;
  messageType?: MessageType;
  timestamp?: Date;
}

export type NotificationServices =  "task" | "department";

export interface SendNofication {
  service: NotificationServices ;
  id: number;
}
