import { Socket } from "socket.io";
import {
  ClientToServerEvents,
  ServerToClientEvents,
  SocketData,
  SendNofication,
} from "../types";
import { getUsersByTaskId } from "../../services";

export const handlePushNotification = (
  socket: Socket<ClientToServerEvents, ServerToClientEvents, any, SocketData>
) => {
  socket.on("send_notification", (data: SendNofication) => {
    try {
      async function createNotification() {
        console.log(
          `[${new Date().toISOString()}] 💬 Notification trigger to user ${
            socket.data.user.id
          }:`,
          JSON.stringify({ data: data })
        );
        switch (data.service) {
          case "task": {
            const users = await getUsersByTaskId(data.id);
            if (users?.length > 0) {
              for (const user of users) {
                socket.to(`user_${user.userId}`).emit("recieve_notification", {
                  services: "task",
                });
              }
            } else {
              console.log(
                `[${new Date().toISOString()}] ❌ No users found in task ${
                  data.id
                }`
              );
            }
            break;
          }
          case "department": {
            // Broadcast notification to all users in the department
            break;
          }
          default: {
            console.error(
              `[${new Date().toISOString()}] ❌ Unknown notification service: ${
                data.service
              }`
            );
            return;
          }
        }
      }
      createNotification();
    } catch (error) {
      console.error(
        `[${new Date().toISOString()}] ❌ Error sending notification:`,
        error
      );
    }
  });
};
