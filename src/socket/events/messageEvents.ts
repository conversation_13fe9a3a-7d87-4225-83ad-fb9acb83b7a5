import { Socket } from "socket.io";
import {
  ClientToServerEvents,
  ServerToClientEvents,
  SocketData,
  SendMessage,
  Message,
} from "../types";
import { createChatMessage, getUsersWithChatId } from "../../services";
import {
  CreateChatMessageData,
  MessageType,
  MessageStatus as STATUS,
} from "../../types";

export const handleSendMessage = (
  socket: Socket<ClientToServerEvents, ServerToClientEvents, any, SocketData>
) => {
  socket.on("send_message", (sendMessage: SendMessage) => {
    try {
      const requestData: CreateChatMessageData = {
        chatId: sendMessage.chatId,
        chatType: sendMessage.chatType,
        userId: socket.data.user.id,
        content: sendMessage.content,
        messageType: sendMessage?.messageType || "text",
        messageStatus: "delivered",
      };

      async function createMessage() {
        const response = await createChatMessage(requestData);
        if (response) {
          const newMessage: Message = {
            messageId: response.id,
            content: response.content,
            sender: socket.data.user,
            createdAt: response.createdAt || new Date(),
            messageType: sendMessage?.messageType as MessageType,
            status: response.messageStatus, // Initial status
          };
          socket
            .to(`chat_${sendMessage.chatId}`)
            .emit("new_message", newMessage);

          const users = await getUsersWithChatId(sendMessage.chatId);
          if (users?.length > 0) {
            for (const user of users) {
              socket.to(`user_${user.userId}`).emit("chat_notification", {
                message: "New message in chat",
                data: {
                  user: socket.data.user,
                  message: requestData,
                },
              });
            }
          } else {
            console.log(
              `[${new Date().toISOString()}] ❌ No users found in chat ${sendMessage.chatId}`
            );
          }
        } else {
          console.error(
            `[${new Date().toISOString()}] ❌ Failed to create message:`,
            response
          );
        }
      }
      createMessage();

      // Confirm to sender that message was sent

      console.log(
        `[${new Date().toISOString()}] 💬 Message sent from user ${socket.data.user.id
        }:`,
        JSON.stringify(sendMessage, null, 2)
      );
    } catch (error) {
      console.error(
        `[${new Date().toISOString()}] ❌ Error sending message:`,
        error
      );
    }
  });
};
