export interface TaskAssignmentWithUser {
  id: number;
  taskId: number;
  userId: number;
  assignedAt: Date;
  assignedBy?: number;
  isLeader: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  // User details
  userEmail?: string;
  userFirstName?: string;
  userLastName?: string;
  userImageUrl?: string;
  // Assigned by user details
  assignedByEmail?: string;
  assignedByFirstName?: string;
  assignedByLastName?: string;
}
