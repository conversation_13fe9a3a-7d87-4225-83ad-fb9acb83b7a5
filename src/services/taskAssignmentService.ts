import { QueryResult } from 'pg';
import { db } from '../database';
import { TaskAssignmentWithUser } from '../types/taskAssignment';

/**
 * Get user IDs and assignedBy information by task ID
 */
export async function getUsersByTaskId(taskId: number): Promise<TaskAssignmentWithUser[]> {
  const query = `
    SELECT
      ta.id,
      ta.task_id as "taskId",
      ta.user_id as "userId",
      ta.assigned_at as "assignedAt",
      ta.assigned_by as "assignedBy",
      ta.is_leader as "isLeader",
      ta.is_active as "isActive",
      ta.created_at as "createdAt",
      ta.updated_at as "updatedAt",
      u.email as "userEmail",
      u.first_name as "userFirstName",
      u.last_name as "userLastName",
      u.image_url as "userImageUrl",
      ab.email as "assignedByEmail",
      ab.first_name as "assignedByFirstName",
      ab.last_name as "assignedByLastName"
    FROM task_assignments ta
    INNER JOIN users u ON ta.user_id = u.id
    LEFT JOIN users ab ON ta.assigned_by = ab.id
    WHERE ta.task_id = $1 AND ta.is_active = true
    ORDER BY ta.assigned_at ASC
  `;

  try {
    const result: QueryResult = await db.query(query, [taskId]);
    return result.rows as TaskAssignmentWithUser[];
  } catch (error) {
    console.error('Error getting users by task ID:', error);
    throw new Error(`Failed to get users for task with ID: ${taskId}`);
  }
}


