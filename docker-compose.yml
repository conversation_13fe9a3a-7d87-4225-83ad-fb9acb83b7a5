version: '3.8'

services:
  # HSM WebSocket Application
  hsm-websocket:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: hsm-websocket-socket
    ports:
      - "8081:8081"
    environment:
      - NODE_ENV=production
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - hsm-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  hsm-network:
    driver: bridge
    name: hsm-network

